import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    singularName: 'release';
    pluralName: 'releases';
    displayName: 'Release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    timezone: Attribute.String;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    singularName: 'release-action';
    pluralName: 'release-actions';
    displayName: 'Release Action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    contentType: Attribute.String & Attribute.Required;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    isEntryValid: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 50;
        },
        number
      >;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    firstname: Attribute.String;
    phone: Attribute.String;
    avatar: Attribute.Media<'images', true>;
    stripe_customer_id: Attribute.String;
    lastname: Attribute.String;
    language: Attribute.Enumeration<['en', 'fr', 'pt']> &
      Attribute.DefaultTo<'en'>;
    is_signedup: Attribute.Boolean & Attribute.DefaultTo<false>;
    city: Attribute.String;
    country: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCityCity extends Schema.CollectionType {
  collectionName: 'cities';
  info: {
    singularName: 'city';
    pluralName: 'cities';
    displayName: 'Cities';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    state_id: Attribute.BigInteger;
    state_code: Attribute.String & Attribute.Required;
    country_id: Attribute.BigInteger;
    country_code: Attribute.String;
    latitude: Attribute.Decimal;
    longitude: Attribute.Decimal;
    flag: Attribute.String;
    wikiDataId: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::city.city', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::city.city', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiCountryCountry extends Schema.CollectionType {
  collectionName: 'countries';
  info: {
    singularName: 'country';
    pluralName: 'countries';
    displayName: 'Countries';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    iso3: Attribute.String;
    iso2: Attribute.String;
    numeric_code: Attribute.String;
    phonecode: Attribute.String;
    capital: Attribute.String;
    currency: Attribute.String;
    currency_name: Attribute.String;
    currency_symbol: Attribute.String;
    tld: Attribute.String;
    native: Attribute.String;
    region: Attribute.String;
    region_id: Attribute.BigInteger;
    subregion: Attribute.String;
    nationality: Attribute.String;
    timezones: Attribute.Text;
    translations: Attribute.Text;
    latitude: Attribute.Decimal;
    longitude: Attribute.Decimal;
    emoji: Attribute.Text & Attribute.DefaultTo<'emoji'>;
    emojiU: Attribute.String;
    flag: Attribute.String;
    wikiDataId: Attribute.String;
    subregion_id: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::country.country',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::country.country',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFilterCategoryFilterCategory extends Schema.CollectionType {
  collectionName: 'filter_categories';
  info: {
    singularName: 'filter-category';
    pluralName: 'filter-categories';
    displayName: 'Filter Category';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    en: Attribute.String;
    pt: Attribute.String;
    fr: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::filter-category.filter-category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::filter-category.filter-category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFundamentalsBalanceSheetFundamentalsBalanceSheet
  extends Schema.CollectionType {
  collectionName: 'fundamentals_balance_sheet';
  info: {
    singularName: 'fundamentals-balance-sheet';
    pluralName: 'fundamentals-balance-sheets';
    displayName: 'Balance Sheet';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::fundamentals-balance-sheet.fundamentals-balance-sheet',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    > &
      Attribute.Required;
    document_current: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
          max: 1;
        },
        number
      > &
      Attribute.DefaultTo<0>;
    document_type_year_or_quarter: Attribute.Enumeration<['q', 'y']> &
      Attribute.Required;
    document_date: Attribute.Date;
    currency_symbol: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 6;
      }>;
    total_assets: Attribute.Float;
    intangible_assets: Attribute.Float;
    earning_assets: Attribute.Float;
    other_current_assets: Attribute.Float;
    total_liab: Attribute.Float;
    total_stockholder_equity: Attribute.Float;
    deferred_long_term_liab: Attribute.Float;
    other_current_liab: Attribute.Float;
    common_stock: Attribute.Float;
    capital_stock: Attribute.Float;
    retained_earnings: Attribute.Float;
    other_liab: Attribute.Float;
    good_will: Attribute.Float;
    other_assets: Attribute.Float;
    cash: Attribute.Float;
    cash_and_equivalents: Attribute.Float;
    total_current_liabilities: Attribute.Float;
    current_deferred_revenue: Attribute.Float;
    net_debt: Attribute.Float;
    short_term_debt: Attribute.Float;
    short_long_term_debt: Attribute.Float;
    short_long_term_debt_total: Attribute.Float;
    other_stockholder_equity: Attribute.Float;
    property_plant_equipment: Attribute.Float;
    total_current_assets: Attribute.Float;
    long_term_investments: Attribute.Float;
    net_tangible_assets: Attribute.Float;
    short_term_investments: Attribute.Float;
    net_receivables: Attribute.Float;
    long_term_debt: Attribute.Float;
    inventory: Attribute.Float;
    accounts_payable: Attribute.Float;
    total_permanent_equity: Attribute.Float;
    noncontrolling_interest_in_consolidated_entity: Attribute.Float;
    temporary_equity_redeemable_noncontrolling_interests: Attribute.Float;
    accumulated_other_comprehensive_income: Attribute.Float;
    additional_paid_in_capital: Attribute.Float;
    common_stock_total_equity: Attribute.Float;
    preferred_stock_total_equity: Attribute.Float;
    retained_earnings_total_equity: Attribute.Float;
    treasury_stock: Attribute.Float;
    accumulated_amortization: Attribute.Float;
    non_current_assets_other: Attribute.Float;
    deferred_long_term_asset_charges: Attribute.Float;
    non_current_assets_total: Attribute.Float;
    capital_lease_obligations: Attribute.Float;
    long_term_debt_total: Attribute.Float;
    non_current_liabilities_other: Attribute.Float;
    non_current_liabilities_total: Attribute.Float;
    negative_goodwill: Attribute.Float;
    warrants: Attribute.Float;
    preferred_stock_redeemable: Attribute.Float;
    capital_surpluse: Attribute.Float;
    liabilities_and_stockholders_equity: Attribute.Float;
    cash_and_short_term_investments: Attribute.Float;
    property_plant_and_equipment_gross: Attribute.Float;
    property_plant_and_equipment_net: Attribute.Float;
    accumulated_depreciation: Attribute.Float;
    net_working_capital: Attribute.Float;
    net_invested_capital: Attribute.Float;
    common_stock_shares_outstanding: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::fundamentals-balance-sheet.fundamentals-balance-sheet',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::fundamentals-balance-sheet.fundamentals-balance-sheet',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFundamentalsCashFlowFundamentalsCashFlow
  extends Schema.CollectionType {
  collectionName: 'fundamentals_cash_flow';
  info: {
    singularName: 'fundamentals-cash-flow';
    pluralName: 'fundamentals-cash-flows';
    displayName: 'Cash Flow';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::fundamentals-cash-flow.fundamentals-cash-flow',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    document_current: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
          max: 1;
        },
        number
      >;
    document_type_year_or_quarter: Attribute.Enumeration<['y', 'q']>;
    document_date: Attribute.Date;
    currency_symbol: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 6;
      }>;
    investments: Attribute.Float;
    change_to_liabilities: Attribute.Float;
    total_cash_flows_from_investing_activities: Attribute.Float;
    net_borrowings: Attribute.Float;
    total_cash_from_financing_activities: Attribute.Float;
    change_to_operating_activities: Attribute.Float;
    net_income: Attribute.Float;
    change_in_cash: Attribute.Float;
    begin_period_cash_flow: Attribute.Float;
    end_period_cash_flow: Attribute.Float;
    total_cash_from_operating_activities: Attribute.Float;
    issuance_of_capital_stock: Attribute.Float;
    depreciation: Attribute.Float;
    other_cash_flows_from_investing_activities: Attribute.Float;
    dividends_paid: Attribute.Float;
    change_to_inventory: Attribute.Float;
    change_to_account_receivables: Attribute.Float;
    sale_purchase_of_stock: Attribute.Float;
    other_cash_flows_from_financing_activities: Attribute.Float;
    change_to_net_income: Attribute.Float;
    capital_expenditures: Attribute.Float;
    change_receivables: Attribute.Float;
    cash_flows_other_operating: Attribute.Float;
    exchange_rate_changes: Attribute.Float;
    cash_and_cash_equivalents_changes: Attribute.Float;
    change_in_working_capital: Attribute.Float;
    stock_based_compensation: Attribute.Float;
    other_non_cash_items: Attribute.Float;
    free_cash_flow: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::fundamentals-cash-flow.fundamentals-cash-flow',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::fundamentals-cash-flow.fundamentals-cash-flow',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFundamentalsIncomeStatementFundamentalsIncomeStatement
  extends Schema.CollectionType {
  collectionName: 'fundamentals_income_statement';
  info: {
    singularName: 'fundamentals-income-statement';
    pluralName: 'fundamentals-income-statements';
    displayName: 'Income Statement';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::fundamentals-income-statement.fundamentals-income-statement',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    document_current: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 0;
          max: 1;
        },
        number
      > &
      Attribute.DefaultTo<0>;
    document_type_year_or_quarter: Attribute.Enumeration<['y', 'q']> &
      Attribute.Required;
    document_date: Attribute.Date;
    currency_symbol: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 6;
      }>;
    research_development: Attribute.Float;
    effect_of_accounting_charges: Attribute.Float;
    income_before_tax: Attribute.Float;
    minority_interest: Attribute.Float;
    net_income: Attribute.Float;
    selling_general_administrative: Attribute.Float;
    selling_and_marketing_expenses: Attribute.Float;
    gross_profit: Attribute.Float;
    reconciled_depreciation: Attribute.Float;
    ebit: Attribute.Float;
    ebitda: Attribute.Float;
    depreciation_and_amortization: Attribute.Float;
    non_operating_income_net_other: Attribute.Float;
    operating_income: Attribute.Float;
    other_operating_expenses: Attribute.Float;
    interest_expense: Attribute.Float;
    tax_provision: Attribute.Float;
    interest_income: Attribute.Float;
    net_interest_income: Attribute.Float;
    extraordinary_items: Attribute.Float;
    non_recurring: Attribute.Float;
    other_items: Attribute.Float;
    income_tax_expense: Attribute.Float;
    total_revenue: Attribute.Float;
    total_operating_expenses: Attribute.Float;
    cost_of_revenue: Attribute.Float;
    total_other_income_expense_net: Attribute.Float;
    discontinued_operations: Attribute.Float;
    net_income_from_continuing_ops: Attribute.Float;
    net_income_applicable_to_common_shares: Attribute.Float;
    preferred_stock_and_other_adjustments: Attribute.Float;
    eps_diluted_current: Attribute.Float;
    eps_diluted_last_date: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::fundamentals-income-statement.fundamentals-income-statement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::fundamentals-income-statement.fundamentals-income-statement',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHistoricalDividendHistoricalDividend
  extends Schema.CollectionType {
  collectionName: 'historical_dividends';
  info: {
    singularName: 'historical-dividend';
    pluralName: 'historical-dividends';
    displayName: 'Historical Dividends';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::historical-dividend.historical-dividend',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    document_date: Attribute.Date;
    document_type_year_or_quarter: Attribute.Enumeration<['q', 'y']> &
      Attribute.Required;
    currency_symbol: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 6;
      }>;
    declaration_date: Attribute.Date;
    record_date: Attribute.Date;
    payment_date: Attribute.Date;
    value: Attribute.Float;
    unadjusted_value: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::historical-dividend.historical-dividend',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::historical-dividend.historical-dividend',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiHistoricalSplitHistoricalSplit
  extends Schema.CollectionType {
  collectionName: 'historical_splits';
  info: {
    singularName: 'historical-split';
    pluralName: 'historical-splits';
    displayName: 'Historical Splits';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::historical-split.historical-split',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    document_date: Attribute.Date;
    split: Attribute.Float;
    insplit: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::historical-split.historical-split',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::historical-split.historical-split',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiListOfTickerListOfTicker extends Schema.CollectionType {
  collectionName: 'list_of_tickers';
  info: {
    singularName: 'list-of-ticker';
    pluralName: 'list-of-tickers';
    displayName: 'List of Tickers';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    symbol_code: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    country_code: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 10;
      }>;
    exchange_code: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    name: Attribute.Text;
    primary_ticker_eodhd: Attribute.String &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    primary_ticker_twelve_data: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    log_eodhd: Attribute.Text;
    is_enable: Attribute.Integer &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 0;
          max: 1;
        },
        number
      > &
      Attribute.DefaultTo<1>;
    sector: Attribute.Relation<
      'api::list-of-ticker.list-of-ticker',
      'oneToOne',
      'api::sector.sector'
    >;
    currency_code: Attribute.String;
    isin: Attribute.String;
    type: Attribute.String;
    another_symbol_codes: Attribute.String;
    url_endpoint: Attribute.String;
    fundamental_data_last_updated: Attribute.Date;
    reason_not_enable: Attribute.Enumeration<
      ['Symbol not found', 'Price not found', 'API 404 response', 'Other']
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::list-of-ticker.list-of-ticker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::list-of-ticker.list-of-ticker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLocationFilterLocationFilter extends Schema.CollectionType {
  collectionName: 'location_filters';
  info: {
    singularName: 'location-filter';
    pluralName: 'location-filters';
    displayName: 'Location filters';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    value: Attribute.String;
    en: Attribute.String;
    fr: Attribute.String;
    pt: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::location-filter.location-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::location-filter.location-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLogOfTickerLogOfTicker extends Schema.CollectionType {
  collectionName: 'logs_of_tickers';
  info: {
    singularName: 'log-of-ticker';
    pluralName: 'log-of-tickers';
    displayName: 'Logs of Tickers';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::log-of-ticker.log-of-ticker',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    latest_api: Attribute.Enumeration<['eodhd', 'twelve_data', 'others']>;
    latest_log: Attribute.Text;
    symbol_code: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    latest_update: Attribute.DateTime;
    quantity_of_balance_sheet_year: Attribute.Integer & Attribute.DefaultTo<0>;
    quantity_of_balance_sheet_quarter: Attribute.Integer &
      Attribute.DefaultTo<0>;
    quantity_of_cash_flow_year: Attribute.Integer & Attribute.DefaultTo<0>;
    quantity_of_cash_flow_quarter: Attribute.Integer & Attribute.DefaultTo<0>;
    quantity_of_income_statement_year: Attribute.Integer &
      Attribute.DefaultTo<0>;
    quantity_of_income_statement_quarter: Attribute.Integer &
      Attribute.DefaultTo<0>;
    quantity_of_dividends: Attribute.Integer;
    quanitty_of_splits: Attribute.Integer;
    start_of_balance_sheet_year: Attribute.Date;
    end_of_balance_sheet_year: Attribute.Date;
    start_of_cash_flow_year: Attribute.Date;
    end_of_cash_flow_year: Attribute.Date;
    start_of_income_statement_year: Attribute.Date;
    end_of_income_statement_year: Attribute.Date;
    start_of_balance_sheet_quarter: Attribute.Date;
    end_of_balance_sheet_quarter: Attribute.Date;
    start_of_cash_flow_quarter: Attribute.Date;
    end_of_cash_flow_quarter: Attribute.Date;
    start_of_income_statement_quarter: Attribute.Date;
    end_of_income_statement_quarter: Attribute.Date;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::log-of-ticker.log-of-ticker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::log-of-ticker.log-of-ticker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPrivacyPolicyPrivacyPolicy extends Schema.SingleType {
  collectionName: 'privacy_policies';
  info: {
    singularName: 'privacy-policy';
    pluralName: 'privacy-policies';
    displayName: 'Privacy Policies';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    text: Attribute.RichText &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::privacy-policy.privacy-policy',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::privacy-policy.privacy-policy',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::privacy-policy.privacy-policy',
      'oneToMany',
      'api::privacy-policy.privacy-policy'
    >;
    locale: Attribute.String;
  };
}

export interface ApiProductProduct extends Schema.CollectionType {
  collectionName: 'products';
  info: {
    singularName: 'product';
    pluralName: 'products';
    displayName: 'Products';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    active: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<true>;
    description: Attribute.Text &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    id_stripe: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    url: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }>;
    products_characteristics: Attribute.Relation<
      'api::product.product',
      'manyToMany',
      'api::product-characteristic.product-characteristic'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product.product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product.product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::product.product',
      'oneToMany',
      'api::product.product'
    >;
    locale: Attribute.String;
  };
}

export interface ApiProductCharacteristicProductCharacteristic
  extends Schema.CollectionType {
  collectionName: 'products_characteristics';
  info: {
    singularName: 'product-characteristic';
    pluralName: 'products-characteristics';
    displayName: 'Products Characteristics';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    products: Attribute.Relation<
      'api::product-characteristic.product-characteristic',
      'manyToMany',
      'api::product.product'
    >;
    characteristic: Attribute.String &
      Attribute.Required &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    is_enable: Attribute.Boolean &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: false;
        };
      }> &
      Attribute.DefaultTo<true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product-characteristic.product-characteristic',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product-characteristic.product-characteristic',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::product-characteristic.product-characteristic',
      'oneToMany',
      'api::product-characteristic.product-characteristic'
    >;
    locale: Attribute.String;
  };
}

export interface ApiProductsPriceProductsPrice extends Schema.CollectionType {
  collectionName: 'products_prices';
  info: {
    singularName: 'products-price';
    pluralName: 'products-prices';
    displayName: 'Products Prices';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    currency: Attribute.Enumeration<['eur', 'usd', 'brl']> &
      Attribute.Required &
      Attribute.DefaultTo<'eur'>;
    active: Attribute.Boolean & Attribute.Required & Attribute.DefaultTo<true>;
    amount: Attribute.Decimal &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    id_stripe: Attribute.String;
    product: Attribute.Relation<
      'api::products-price.products-price',
      'oneToOne',
      'api::product.product'
    >;
    usage_type: Attribute.Enumeration<['licensed', 'metered']> &
      Attribute.DefaultTo<'licensed'>;
    interval: Attribute.Enumeration<['day', 'week', 'month', 'year']>;
    interval_count: Attribute.Integer &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    billing_scheme: Attribute.Enumeration<['per_unit', 'tiered']> &
      Attribute.Required &
      Attribute.DefaultTo<'per_unit'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::products-price.products-price',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::products-price.products-price',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiRegionRegion extends Schema.CollectionType {
  collectionName: 'regions';
  info: {
    singularName: 'region';
    pluralName: 'regions';
    displayName: 'Regions';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    translations: Attribute.JSON;
    flag: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<1>;
    wikiDataId: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::region.region',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::region.region',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSectorSector extends Schema.CollectionType {
  collectionName: 'sectors';
  info: {
    singularName: 'sector';
    pluralName: 'sectors';
    displayName: 'Sector';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    en: Attribute.String;
    fr: Attribute.String;
    pt: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::sector.sector',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::sector.sector',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSelectedFilterSelectedFilter extends Schema.CollectionType {
  collectionName: 'selected_filters';
  info: {
    singularName: 'selected-filter';
    pluralName: 'selected-filters';
    displayName: 'Selected Filters';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    payload: Attribute.JSON;
    type: Attribute.Enumeration<['free,', 'premium']>;
    name: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::selected-filter.selected-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::selected-filter.selected-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiStateState extends Schema.CollectionType {
  collectionName: 'states';
  info: {
    singularName: 'state';
    pluralName: 'states';
    displayName: 'States';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    country_code: Attribute.String & Attribute.Required;
    fips_code: Attribute.String;
    type: Attribute.String;
    latitude: Attribute.Decimal;
    longitude: Attribute.Decimal;
    flag: Attribute.String;
    wikiDataId: Attribute.String;
    country_id: Attribute.Integer;
    iso2: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::state.state',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::state.state',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiStatisticsOfApiStatisticsOfApi
  extends Schema.CollectionType {
  collectionName: 'statistics_of_apis';
  info: {
    singularName: 'statistics-of-api';
    pluralName: 'statistics-of-apis';
    displayName: 'Statistics of API';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    request_date: Attribute.Date & Attribute.Required;
    latest_api: Attribute.Enumeration<['eodhd', 'twelve_data', 'others']> &
      Attribute.Required;
    api_credit_consumption: Attribute.Integer &
      Attribute.Required &
      Attribute.DefaultTo<0>;
    request_url: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::statistics-of-api.statistics-of-api',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::statistics-of-api.statistics-of-api',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiStatisticsOfFilterStatisticsOfFilter
  extends Schema.CollectionType {
  collectionName: 'statistics_of_filter';
  info: {
    singularName: 'statistics-of-filter';
    pluralName: 'statistics-of-filters';
    displayName: 'Statistics of Filters';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    indicator_name: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
    min: Attribute.Float;
    max: Attribute.Float;
    step: Attribute.Decimal & Attribute.DefaultTo<0.5>;
    search_type: Attribute.Enumeration<['simple', 'advanced']> &
      Attribute.DefaultTo<'simple'>;
    label: Attribute.JSON;
    filter_category: Attribute.Relation<
      'api::statistics-of-filter.statistics-of-filter',
      'oneToOne',
      'api::filter-category.filter-category'
    >;
    filter_type: Attribute.Enumeration<['normal', 'percent', 'million']> &
      Attribute.DefaultTo<'normal'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::statistics-of-filter.statistics-of-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::statistics-of-filter.statistics-of-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiStatisticsOfTickerStatisticsOfTicker
  extends Schema.CollectionType {
  collectionName: 'statistics_of_tickers';
  info: {
    singularName: 'statistics-of-ticker';
    pluralName: 'statistics-of-tickers';
    displayName: 'Statistics of Tickers';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ticker_internal: Attribute.Relation<
      'api::statistics-of-ticker.statistics-of-ticker',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    symbol_code: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    price: Attribute.Float;
    dividend_yield: Attribute.Float;
    eps_current: Attribute.Float;
    eps_last_year: Attribute.Float;
    pe: Attribute.Float;
    peg_ratio: Attribute.Float;
    bvps: Attribute.Float;
    price_to_book: Attribute.Float;
    ebitda: Attribute.Float;
    ebitda_margin: Attribute.Float;
    ebit: Attribute.Float;
    operation_margin: Attribute.Float;
    net_margin: Attribute.Float;
    gross_margin: Attribute.Float;
    price_working_capital_share: Attribute.Float;
    ebit_ratio: Attribute.Float;
    net_debt: Attribute.Float;
    net_debt_ebit: Attribute.Float;
    net_debt_ebitda: Attribute.Float;
    market_capitalization: Attribute.Float;
    ev_ebit: Attribute.Float;
    net_debt_shareholders_equity: Attribute.Float;
    price_sales_ratio: Attribute.Float;
    roe: Attribute.Float;
    roa: Attribute.Float;
    roic: Attribute.Float;
    current_ratio: Attribute.Float;
    shareholder_equity_ratio: Attribute.Float;
    total_debt_to_total_assets_ratio: Attribute.Float;
    asset_turnover: Attribute.Float;
    payout_ratio: Attribute.Float;
    price_to_cash_flow: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::statistics-of-ticker.statistics-of-ticker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::statistics-of-ticker.statistics-of-ticker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSubRegionSubRegion extends Schema.CollectionType {
  collectionName: 'sub_regions';
  info: {
    singularName: 'sub-region';
    pluralName: 'sub-regions';
    displayName: 'Sub Regions';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    translations: Attribute.JSON;
    flag: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<1>;
    wikiDataId: Attribute.String;
    region_id: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::sub-region.sub-region',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::sub-region.sub-region',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTermsUseTermsUse extends Schema.SingleType {
  collectionName: 'terms_uses';
  info: {
    singularName: 'terms-use';
    pluralName: 'terms-uses';
    displayName: 'Terms Uses';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    text: Attribute.RichText &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::terms-use.terms-use',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::terms-use.terms-use',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::terms-use.terms-use',
      'oneToMany',
      'api::terms-use.terms-use'
    >;
    locale: Attribute.String;
  };
}

export interface ApiUsersFilterUsersFilter extends Schema.CollectionType {
  collectionName: 'users_filters';
  info: {
    singularName: 'users-filter';
    pluralName: 'users-filters';
    displayName: 'Users Filters';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String;
    user: Attribute.Relation<
      'api::users-filter.users-filter',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    payload: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::users-filter.users-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::users-filter.users-filter',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWalletWallet extends Schema.CollectionType {
  collectionName: 'wallets';
  info: {
    singularName: 'wallet';
    pluralName: 'wallets';
    displayName: 'Wallet';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    currency: Attribute.Enumeration<['USD', 'EUR', 'BRL']>;
    amount_invested: Attribute.Decimal & Attribute.DefaultTo<0>;
    yield: Attribute.Decimal;
    dividends_amount: Attribute.Decimal & Attribute.DefaultTo<0>;
    user: Attribute.Relation<
      'api::wallet.wallet',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    patrimony: Attribute.Float;
    uuid: Attribute.UID;
    balance: Attribute.Float & Attribute.DefaultTo<0>;
    unrealized_gain_loss: Attribute.Float;
    realized_sell: Attribute.Float;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::wallet.wallet',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::wallet.wallet',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWalletItemWalletItem extends Schema.CollectionType {
  collectionName: 'wallet_items';
  info: {
    singularName: 'wallet-item';
    pluralName: 'wallet-items';
    displayName: 'Wallet Items';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    list_of_ticker: Attribute.Relation<
      'api::wallet-item.wallet-item',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    currency: Attribute.Enumeration<['EUR', 'USD', 'BRL']>;
    quantity: Attribute.Decimal & Attribute.Required;
    avg_price: Attribute.Decimal & Attribute.Required;
    casted_current_price: Attribute.Decimal & Attribute.Required;
    total_current_price: Attribute.Decimal & Attribute.Required;
    wallet_percent: Attribute.Decimal & Attribute.Required;
    yield: Attribute.Decimal;
    total_casted_current_price: Attribute.Decimal & Attribute.Required;
    total_casted_avg_price: Attribute.Decimal;
    wallet: Attribute.Relation<
      'api::wallet-item.wallet-item',
      'oneToOne',
      'api::wallet.wallet'
    >;
    original_current_price: Attribute.Float;
    is_enabled: Attribute.Boolean & Attribute.DefaultTo<true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::wallet-item.wallet-item',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::wallet-item.wallet-item',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWalletTransactionWalletTransaction
  extends Schema.CollectionType {
  collectionName: 'wallet_transactions';
  info: {
    singularName: 'wallet-transaction';
    pluralName: 'wallet-transactions';
    displayName: 'Wallet Transaction';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    list_of_ticker: Attribute.Relation<
      'api::wallet-transaction.wallet-transaction',
      'oneToOne',
      'api::list-of-ticker.list-of-ticker'
    >;
    currency: Attribute.Enumeration<['EUR', 'USD', 'BRL']> &
      Attribute.Required &
      Attribute.DefaultTo<'EUR'>;
    price: Attribute.Decimal & Attribute.Required;
    quantity: Attribute.Decimal & Attribute.Required;
    type: Attribute.Enumeration<
      ['BUY', 'SELL', 'DEPOSIT', 'RETRAIT', 'DIVIDENDS_PAID']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'BUY'>;
    wallet: Attribute.Relation<
      'api::wallet-transaction.wallet-transaction',
      'oneToOne',
      'api::wallet.wallet'
    > &
      Attribute.Required;
    casted_price: Attribute.Decimal;
    multiplier: Attribute.Decimal;
    transaction_date: Attribute.Date;
    current_casted_price: Attribute.Float;
    current_price: Attribute.Float;
    note: Attribute.Text;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::wallet-transaction.wallet-transaction',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::wallet-transaction.wallet-transaction',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWatchlistWatchlist extends Schema.CollectionType {
  collectionName: 'watchlists';
  info: {
    singularName: 'watchlist';
    pluralName: 'watchlists';
    displayName: 'Watchlist';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    user: Attribute.Relation<
      'api::watchlist.watchlist',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    price: Attribute.Decimal;
    is_enable: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
    statistics_of_ticker: Attribute.Relation<
      'api::watchlist.watchlist',
      'oneToOne',
      'api::statistics-of-ticker.statistics-of-ticker'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::watchlist.watchlist',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::watchlist.watchlist',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'api::city.city': ApiCityCity;
      'api::country.country': ApiCountryCountry;
      'api::filter-category.filter-category': ApiFilterCategoryFilterCategory;
      'api::fundamentals-balance-sheet.fundamentals-balance-sheet': ApiFundamentalsBalanceSheetFundamentalsBalanceSheet;
      'api::fundamentals-cash-flow.fundamentals-cash-flow': ApiFundamentalsCashFlowFundamentalsCashFlow;
      'api::fundamentals-income-statement.fundamentals-income-statement': ApiFundamentalsIncomeStatementFundamentalsIncomeStatement;
      'api::historical-dividend.historical-dividend': ApiHistoricalDividendHistoricalDividend;
      'api::historical-split.historical-split': ApiHistoricalSplitHistoricalSplit;
      'api::list-of-ticker.list-of-ticker': ApiListOfTickerListOfTicker;
      'api::location-filter.location-filter': ApiLocationFilterLocationFilter;
      'api::log-of-ticker.log-of-ticker': ApiLogOfTickerLogOfTicker;
      'api::privacy-policy.privacy-policy': ApiPrivacyPolicyPrivacyPolicy;
      'api::product.product': ApiProductProduct;
      'api::product-characteristic.product-characteristic': ApiProductCharacteristicProductCharacteristic;
      'api::products-price.products-price': ApiProductsPriceProductsPrice;
      'api::region.region': ApiRegionRegion;
      'api::sector.sector': ApiSectorSector;
      'api::selected-filter.selected-filter': ApiSelectedFilterSelectedFilter;
      'api::state.state': ApiStateState;
      'api::statistics-of-api.statistics-of-api': ApiStatisticsOfApiStatisticsOfApi;
      'api::statistics-of-filter.statistics-of-filter': ApiStatisticsOfFilterStatisticsOfFilter;
      'api::statistics-of-ticker.statistics-of-ticker': ApiStatisticsOfTickerStatisticsOfTicker;
      'api::sub-region.sub-region': ApiSubRegionSubRegion;
      'api::terms-use.terms-use': ApiTermsUseTermsUse;
      'api::users-filter.users-filter': ApiUsersFilterUsersFilter;
      'api::wallet.wallet': ApiWalletWallet;
      'api::wallet-item.wallet-item': ApiWalletItemWalletItem;
      'api::wallet-transaction.wallet-transaction': ApiWalletTransactionWalletTransaction;
      'api::watchlist.watchlist': ApiWatchlistWatchlist;
    }
  }
}
