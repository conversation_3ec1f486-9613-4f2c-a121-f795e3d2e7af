import {CashFlowController} from "./controller/CashFlowController";
import {BalanceSheetController} from "./controller/balanceSheetController";
import {getDividendsController} from "./controller/dividendsController";
import {EPSController} from "./controller/epsController";
import {getTickersUpdatedLastXDays, processS3Files, searchFileTickerName} from "./controller/filesController";
import {FiltersController} from "./controller/fitlersController";
import {IncomeStatementController} from "./controller/incomeStatementController";
import {PriceController} from "./controller/priceController";
import {SplitController} from "./controller/splitsController";
import {StatisticsOfTickerController} from "./controller/statisticsOfTickerController";
import {getAllListOfTickers, getOutdatedListOfTickers, getTickersByPrimaryTickerCodes, populateListOfTickers, saveTickersIntoBucket} from "./controller/tickersController";
import {ListOfTickers} from "./entities/consolidated_data/ListOfTickers";
import {addLogJobExecution} from "./lib/agenda";
import {iMessageSQS} from "./repositories/iMessageSQS";
import {DynamoStreamEventType} from "./utils/types/dynamo/DynamoStreamEventType";
import {ListofTickerIdEventType} from "./utils/types/events/ListofTickerIdEventType";
import {LogLevel} from "./utils/types/logs/log";
import {TriggerS3EventType} from "./utils/types/s3/TriggerS3EventType";

/*
 *
 */
export const populateStockTickers = async (exchangeCodes?: string[]) => {
    try {
        await populateListOfTickers(exchangeCodes);
        return "Process of populating Stock Tickers has started";
    } catch (error: unknown) {
        console.log(error instanceof Error ? error.message : String(error));
    }
};

/**
 * Retrieves fundamental data for a list of tickers and saves it to S3
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @returns Processed ticker data or undefined on error
 */
export const getFundamentalDataAndSaveToS3 = async (tickerCodes?: string[]) => {
    try {
        let data: ListOfTickers[];

        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            console.log(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`);
            addLogJobExecution(LogLevel.INFO, "getFundamentalDataAndSaveToS3", "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            // Use the existing logic to get outdated tickers
            data = await getOutdatedListOfTickers();
            console.log(`Processing ${data.length} outdated tickers`);
            addLogJobExecution(LogLevel.INFO, "getFundamentalDataAndSaveToS3", "Processing outdated tickers", {tickersLength: data.length});
        }

        await saveTickersIntoBucket(data);
        return data;
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Processes files from S3 bucket events or retrieves files for tickers updated today
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @returns Status message or error message
 */
export const processS3FilesToAddToDatabase = async (tickerCodes?: string[]) => {
    try {
        let data: ListOfTickers[];

        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            console.log(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`);
            addLogJobExecution(LogLevel.INFO, "processS3FilesToAddToDynamo", "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            // Use the existing logic to get tickers updated last 7 days
            data = await getTickersUpdatedLastXDays();
            console.log(`Processing ${data.length} tickers updated last 7 days`);
            addLogJobExecution(LogLevel.INFO, "processS3FilesToAddToDynamo", "Processing tickers updated last 7 days", {tickersLength: data.length});
        }
        await processS3Files(data);
        return "Processed files for tickers updated today";
    } catch (error: any) {
        console.log(error.message);
        return error.message;
    }
};

/**
 * Retrieves dividend information for specified tickers
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @returns Status message or undefined on error
 */
export const getDividends = async (tickerCodes?: string[]) => {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            console.log(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`);
            addLogJobExecution(LogLevel.INFO, "processS3FilesToAddToDynamo", "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            addLogJobExecution(LogLevel.INFO, "processS3FilesToAddToDynamo", "Processing all tickers", {tickersLength: data.length});
        }
        await getDividendsController(data);
        return "Dividends in queue";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Retrieves and processes stock split information
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @returns Status message or undefined on error
 */
export const getSplits = async (tickerCodes?: string[]) => {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            console.log(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`);
            addLogJobExecution(LogLevel.INFO, "getSplits", "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            addLogJobExecution(LogLevel.INFO, "getSplits", "Processing all tickers", {tickersLength: data.length});
        }
        const splitController = new SplitController();
        await splitController.fillSplits(data);
        return "Splits in queue";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Retrieves current price data for specified tickers
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @returns Status message or undefined on error
 */
export const getPrices = async (tickerCodes?: string[]) => {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            console.log(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`);
            addLogJobExecution(LogLevel.INFO, "getTicketPrice", "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            addLogJobExecution(LogLevel.INFO, "getTicketPrice", "Processing all tickers", {tickersLength: data.length});
        }
        const priceController = new PriceController();
        const [valid_prices, invalid_prices] = await priceController.getPrices(data);
        addLogJobExecution(LogLevel.INFO, "getTicketPrice", "Retrieved prices for tickers", {
            tickersLength: data.length,
            validPricesLength: valid_prices.length,
            invalidPricesLength: invalid_prices.length,
        });
        if (valid_prices.length > 0) {
            addLogJobExecution(LogLevel.INFO, "getTicketPrice", "Setting prices for tickers", {
                validPricesLength: valid_prices.length,
            });
            await priceController.setPrices(valid_prices);
        }
        if (invalid_prices.length > 0) {
            addLogJobExecution(LogLevel.INFO, "getTicketPrice", "Setting invalid prices for tickers", {
                invalidPricesLength: invalid_prices.length,
            });
            await priceController.setInvalidPrices(invalid_prices);
        }
        addLogJobExecution(LogLevel.INFO, "getTicketPrice", "Prices set for tickers", {});
        return "Ok";
    } catch (error) {
        console.log(error);
    }
};

/**
 * Retrieves statistical data for specified tickers
 * @param tickerCodes - Optional array of primary_ticker_eodhd codes (e.g., ['NVDA.US', 'XP.US']). If provided, only these tickers will be processed instead of outdated tickers.
 * @returns Status message
 */
export const getStatistics = async (tickerCodes?: string[]) => {
    try {
        let data: ListOfTickers[];
        if (tickerCodes && tickerCodes.length > 0) {
            // Use the provided ticker codes to get specific tickers
            data = await getTickersByPrimaryTickerCodes(tickerCodes);
            console.log(`Processing ${data.length} specific tickers: ${tickerCodes.join(", ")}`);
            addLogJobExecution(LogLevel.INFO, "getStatistics", "Processing specific tickers", {tickerCodesLength: tickerCodes.length});
        } else {
            data = await getAllListOfTickers();
            addLogJobExecution(LogLevel.INFO, "getStatistics", "Processing all tickers", {tickersLength: data.length});
        }
        const statisticsController = new StatisticsOfTickerController();
        await statisticsController.getTickersStatisticsAndCalculate(data);
        addLogJobExecution(LogLevel.INFO, "getStatistics", "Statistics set for tickers", {tickersLength: data.length});
    } catch (error) {
        console.log(error);
        addLogJobExecution(LogLevel.ERROR, "getStatistics", "Error when try to get statistics", {error: error instanceof Error ? error.message : String(error)});
    }
    return "in queue";
};

/**
 * Updates EPS (Earnings Per Share) data for all tickers
 * @returns Status message
 */
export const setEPS = async () => {
    const eps = new EPSController();
    await eps.getTickers();
    await eps.setEPS();
    return "Set EPS";
};

/**
 * Searches for file highlights for specified tickers
 * @param event - Event containing list of ticker IDs
 * @returns Status message
 */
export const getFileHighLights = async (event: ListofTickerIdEventType) => {
    const statisticsController = new StatisticsOfTickerController();
    await statisticsController.getTickers(event);
    const tickers = statisticsController.statisticsRepository.statistics_of_tickers;
    await searchFileTickerName(tickers);
    return "Finished";
};

/**
 * Populates filter data
 * @param event - Event data (currently unused)
 * @returns Status message
 */
export const setFilters = async (event: any) => {
    const filtersController = new FiltersController();
    await filtersController.fillFilters();
    return "in queue";
};
