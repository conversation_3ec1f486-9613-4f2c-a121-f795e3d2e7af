import {addLogJobExecution} from "@/lib/agenda";
import {iFundamentalData} from "../entities/EOD/iFundamentalData";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../entities/consolidated_data/StatisticsOfTickers";
import {LogsOfTickersExecute} from "../execute/logsOfTickersExecute";
import {FundamentalDataRepository} from "../repositories/implements/FundamentaDataRepository";
import {FundamentalDataType} from "../utils/types/EODHD/FundamentalDataType";
import {S3ObjectEvent, S3Record} from "../utils/types/s3/TriggerS3EventType";
import {DynamoController} from "./dynamoController";
import {Op} from "sequelize";
import {LogLevel} from "@/utils/types/logs/log";
import {BalanceSheetController} from "./balanceSheetController";
import {IncomeStatementController} from "./incomeStatementController";
import {CashFlowController} from "./CashFlowController";
import {LogsController} from "./logsController";

/**
 * Get tickers that had their fundamental_data_last_updated changed today
 * @returns Array of tickers updated today
 */
export async function getTickersUpdatedLastXDays(lastXDays: number = 7): Promise<ListOfTickers[]> {
    console.log("Retrieving tickers updated:" + lastXDays + " days ago today");
    addLogJobExecution(LogLevel.INFO, "getTickersUpdatedLastXDays", "Retrieving tickers updated:" + lastXDays + " days ago today", {lastXDays});
    const startTime = Date.now();

    // Create start of dateStart date
    const dateStart = new Date();
    dateStart.setDate(dateStart.getDate() - lastXDays);
    dateStart.setHours(0, 0, 0, 0);

    console.log(`Date (start): ${dateStart.toISOString()}`);

    const params: any = {
        attributes: ["primary_ticker_eodhd", "id"],
        order: [["id", "ASC"]],
        where: {
            is_enable: 1,
            fundamental_data_last_updated: {
                [Op.gte]: dateStart,
            },
        },
    };

    const updatedTickers = await ListOfTickers.findAll(params);
    const executionTime = Date.now() - startTime;

    console.log(`Found ${updatedTickers.length} tickers updated in past ${lastXDays} days in ${executionTime}ms`);
    addLogJobExecution(LogLevel.INFO, "getTickersUpdatedLastXDays", "`Found ${updatedTickers.length} tickers updated in past ${lastXDays} days in ${executionTime}ms`", {
        updatedTickersLength: updatedTickers.length,
    });
    return updatedTickers;
}

export async function processS3Files(updatedTickers: ListOfTickers[]): Promise<any> {
    addLogJobExecution(LogLevel.INFO, "processS3Files", "Processing S3 files", {});

    const fundamentalData = new FundamentalDataRepository();
    const dynamoController = new DynamoController();

    for (let i = 0; i < updatedTickers.length; i++) {
        const ticker = updatedTickers[i];
        const key = `fundamentals_${ticker.primary_ticker_eodhd}_eod.json`;
        try {
            console.log(`Processing ticker ${i + 1}/${updatedTickers.length}: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            addLogJobExecution(LogLevel.INFO, "processS3Files", `Processing ticker ${i + 1}/${updatedTickers.length}: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`, {}, ticker.id);
            const file: FundamentalDataType = await fundamentalData.get(key);
            if (!file || Object.keys(file).length === 0) {
                console.log(`No file found for ${ticker.primary_ticker_eodhd}`);
                addLogJobExecution(LogLevel.WARNING, "processS3Files", `No file found for ${ticker.primary_ticker_eodhd}`, {}, ticker.id);
                continue;
            }
            await saveFundamentalData(file);
            addLogJobExecution(LogLevel.INFO, "processS3Files", "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully", {}, ticker.id);
        } catch (error: any) {
            console.log(`Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`);
            addLogJobExecution(LogLevel.ERROR, "processS3Files", `Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`, {}, ticker.id);
        }
    }
}

export async function saveFundamentalData(data: FundamentalDataType) {
    const {ticker_internal_id, Financials} = data;
    const id = parseInt(ticker_internal_id || "0");
    try {
        const balance_sheet = Financials?.Balance_Sheet || {};
        const income_statement = Financials?.Income_Statement || {};
        const cash_flow = Financials?.Cash_Flow || {};

        const balanceSheet = new BalanceSheetController();
        const balanceSheetData = await balanceSheet.parseData(id, balance_sheet);
        console.log("balancesheet was QUEUED");

        const incomeStatement = new IncomeStatementController();
        const incomeStatementData = await incomeStatement.parseData(id, income_statement);
        console.log("income statement was QUEUED");

        const cashFlow = new CashFlowController();
        const cashFlowData = await cashFlow.parseData(id, cash_flow);
        console.log("cash flow was QUEUED");

        const logsController = new LogsController();

        if (!balanceSheetData || !incomeStatementData || !cashFlowData) {
            logsController.tickerUpdatedEODHD(id, "Missing data");
            throw new Error("Missing data");
        }

        logsController.dynamoDBQueued(
            id,
            "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully",
            balanceSheetData.quantity_of_balance_sheet_year,
            balanceSheetData.quantity_of_balance_sheet_quarter,
            cashFlowData.quantity_of_cash_flow_year,
            cashFlowData.quantity_of_cash_flow_quarter,
            incomeStatementData.quantity_of_income_statement_year,
            incomeStatementData.quantity_of_income_statement_quarter,
            balanceSheetData.start_of_balance_sheet_year,
            balanceSheetData.end_of_balance_sheet_year,
            cashFlowData.start_of_cash_flow_year,
            cashFlowData.end_of_cash_flow_year,
            incomeStatementData.start_of_income_statement_year,
            incomeStatementData.end_of_income_statement_year,
            balanceSheetData.start_of_balance_sheet_quarter,
            balanceSheetData.end_of_balance_sheet_quarter,
            cashFlowData.start_of_cash_flow_quarter,
            cashFlowData.end_of_cash_flow_quarter,
            incomeStatementData.start_of_income_statement_quarter,
            incomeStatementData.end_of_income_statement_quarter,
        );
        addLogJobExecution(LogLevel.INFO, "saveFundamentalData", "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully", {}, id);
    } catch (err: any) {
        addLogJobExecution(LogLevel.ERROR, "saveFundamentalData", "Error when try to save fundamental data", {error: err instanceof Error ? err.message : String(err)}, id);
        console.log("Error saveFundamentalData", err);
    }
}

export async function moveFilesController(files: any, keys: any) {
    const fundamentalData = new FundamentalDataRepository();

    for (let i = 0; i < files.length; i++) {
        try {
            await fundamentalData.move(keys[i].Key, "parsed");
            await LogsOfTickersExecute.saveLogToMoveFile(files[i].ticker_internal_id, "File moved successfully");
        } catch (error: any) {
            console.log(error.message);
        }
    }
}

export async function searchFileTickerName(tickers: StatisticsOfTicker[]) {
    const fundamentalData = new FundamentalDataRepository();

    for (let i = 0; i < tickers.length; i++) {
        const ticker = tickers[i];

        const file: iFundamentalData = await fundamentalData.get(`fundamentals_${ticker.symbol_code}_eod.json`);

        const {Highlights: Highlights} = file;

        const dividend = Highlights?.DividendYield || 0;

        ticker.dividend_yield = dividend * 100;

        console.log("dividend ", ticker.dividend_yield, ticker.symbol_code, " saved");

        await ticker.save();
    }
}
