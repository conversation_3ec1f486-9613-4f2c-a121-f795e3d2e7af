import styles from "./job-progress-card.module.css";

// Mock data for currently running jobs
const runningJobs = [
    {
        id: "1",
        name: "send-email",
        progress: 75,
        status: "running",
        startedAt: "2 minutes ago",
    },
    {
        id: "2",
        name: "process-payment",
        progress: 45,
        status: "running",
        startedAt: "5 minutes ago",
    },
    {
        id: "3",
        name: "generate-report",
        progress: 90,
        status: "running",
        startedAt: "8 minutes ago",
    },
];

export function JobProgressCard() {
    return (
        <div className={styles.card}>
            <div className={styles.header}>
                <h3 className={styles.title}>Running Jobs</h3>
                <div className={styles.badge}>{runningJobs.length} Active</div>
            </div>
            <div className={styles.content}>
                {runningJobs.length === 0 ? (
                    <p className={styles.emptyState}>No jobs currently running</p>
                ) : (
                    <div className={styles.jobsList}>
                        {runningJobs.map((job) => (
                            <div key={job.id} className={styles.jobItem}>
                                <div className={styles.jobInfo}>
                                    <p className={styles.jobName}>{job.name}</p>
                                    <p className={styles.jobTime}>Started {job.startedAt}</p>
                                </div>
                                <div className={styles.progressSection}>
                                    <div className={styles.progressContainer}>
                                        <div className={styles.progressBar} style={{width: `${job.progress}%`}} />
                                    </div>
                                    <span
                                        className={styles.progressInnerText}
                                        style={{
                                            color: job.progress > 45 ? "var(--background)" : "var(--foreground)",
                                            opacity: job.progress > 10 ? 1 : 0,
                                        }}>
                                        {job.progress}%
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
