.container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tableContainer {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.tableHeader {
    background-color: var(--muted);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tableHead {
    padding: 0.75rem;
    text-align: left;
    color: var(--muted-foreground);
    font-weight: 500;
}

.tableRow {
    border-bottom: 1px solid var(--border);
}

.tableRow:last-child {
    border-bottom: none;
}

.tableCell {
    padding: 0.75rem;
    font-size: 0.875rem;
}

.result {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.completed {
    background-color: var(--primary-light, #e6f7ff);
    color: var(--primary, #0070f3);
}

.failed {
    background-color: var(--destructive-light, #ffe6e6);
    color: var(--destructive, #ff0000);
}

.started {
    background-color: var(--warning-light, #fff8e6);
    color: var(--warning, #f59e0b);
}

.loading,
.error,
.empty {
    padding: 2rem;
    text-align: center;
    color: var(--muted-foreground, #666);
    font-size: 0.875rem;
}

.error {
    color: var(--destructive, #ff0000);
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
}

.paginationButton {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
}

.paginationIcon {
    height: 1rem;
    width: 1rem;
}

.paginationInfo {
    font-size: 0.875rem;
}

.progressContainer {
    position: relative;
    width: 100%;
    height: 20px;
    background-color: var(--muted);
    border-radius: 10px;
    overflow: hidden;
}

.progressBar {
    height: 100%;
    background-color: var(--primary);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progressText {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--foreground);
}
