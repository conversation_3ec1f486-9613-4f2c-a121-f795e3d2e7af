import {DeleteMessageCommand, SendMessageCommand} from "@aws-sdk/client-sqs";
import _ from "lodash";
import {Op} from "sequelize";
import {BalanceSheet} from "../../entities/consolidated_data/BalanceSheet";
import {IncomeStatement} from "../../entities/consolidated_data/IncomeStatement";
import {StatisticsOfTicker} from "../../entities/consolidated_data/StatisticsOfTickers";
import {StatisticOfTickerIndicatorRepository} from "./StatisticOfTickerIndicatorRepository";
import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {iStatisticsOfTicker} from "@/entities/consolidated_data/iStatisticsOfTicker";
import {LogsController} from "@/controller/logsController";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";

export class StatisticsOfTickerRepository {
    statistics_of_tickers: StatisticsOfTicker[];

    async getTickers(TickerInternalId?: number[]): Promise<void> {
        const date = new Date();

        const minutes = date.getMinutes();

        const page = minutes < 10 ? 0 : Math.floor(minutes / 10);

        const tickers_count = await StatisticsOfTicker.count();

        const mod = tickers_count % 6 || 0;
        const limit = Math.trunc(tickers_count / 6) + mod;

        console.log("page", page);

        const params: any = {};

        console.log(TickerInternalId, TickerInternalId && Array.isArray(TickerInternalId));

        if (TickerInternalId && Array.isArray(TickerInternalId)) {
            params.where = {
                ticker_internal_id: {
                    [Op.in]: TickerInternalId,
                },
            };
        } else {
            params.limit = limit;
            params.offset = page * limit;
            params.order = [["id", "ASC"]];
        }

        const tickers = await StatisticsOfTicker.findAll(params);

        this.statistics_of_tickers = tickers;
    }

    async getTickerFundamentalData(ticker_internal_id: number): Promise<{
        income_statement: IncomeStatement[];
        balance_sheet: BalanceSheet[];
        income_statement_last_year: IncomeStatement[];
    }> {
        const balance_sheet = await BalanceSheet.findAll({
            where: {ticker_internal_id, document_type_year_or_quarter: "q"},
            limit: 4,
            order: [["document_date", "DESC"]],
        });

        const income_statement = await IncomeStatement.findAll({
            where: {ticker_internal_id, document_type_year_or_quarter: "q"},
            limit: 4,
            order: [["document_date", "DESC"]],
        });
        let year = new Date().getFullYear() - 1;

        if (income_statement.length > 0) {
            year = parseInt(income_statement[0].document_date.split("-")[0]) - 1;
        }

        console.log("year", year);

        const income_statement_last_year = await IncomeStatement.findAll({
            where: {
                ticker_internal_id,
                document_type_year_or_quarter: "q",
                document_date: {[Op.lte]: `${year}-12-31`},
            },
            limit: 4,
            order: [["document_date", "DESC"]],
        });

        return {income_statement, balance_sheet, income_statement_last_year};
    }

    async getTickersStatisticsAndCalculate(tickers: ListOfTickers[]) {
        this.statistics_of_tickers = await StatisticsOfTicker.findAll({
            where: {
                ticker_internal_id: {
                    [Op.in]: tickers.map((ticker) => ticker.id || 0),
                },
            },
        });
        for (let i = 0; i < this.statistics_of_tickers.length; i++) {
            console.log(`[${i + 1}/${this.statistics_of_tickers.length}] Processing ticker: ${this.statistics_of_tickers[i].symbol_code} (ID: ${this.statistics_of_tickers[i].ticker_internal_id})`);
            try {
                const ticker_internal_id = this.statistics_of_tickers[i].ticker_internal_id;
                const {balance_sheet, income_statement, income_statement_last_year} = await this.getTickerFundamentalData(ticker_internal_id);
                this.calculateStatistics(this.statistics_of_tickers[i].dataValues, balance_sheet, income_statement, income_statement_last_year);
            } catch (error: any) {
                console.log("getTickersStatisticsAndCalculate error", error.message);
                await LogsController.saveError(this.statistics_of_tickers[i].ticker_internal_id, error.message);
                addLogJobExecution(
                    LogLevel.ERROR,
                    "getTickersStatisticsAndCalculate",
                    "Error when try to get statistics",
                    {error: error instanceof Error ? error.message : String(error)},
                    this.statistics_of_tickers[i].ticker_internal_id,
                );
                continue;
            }
        }
    }

    async calculateStatistics(ticker: iStatisticsOfTicker, balance_sheet: BalanceSheet[], income_statement: IncomeStatement[], income_statement_last_year: IncomeStatement[]) {
        try {
            const oldTicker = {...ticker};

            const indicator = new StatisticOfTickerIndicatorRepository(ticker);

            console.log("**** Symbol ****", ticker.symbol_code);

            await indicator.get_last_balance_sheet_yearly(balance_sheet);
            await indicator.get_last_income_statement_yearly(income_statement, income_statement_last_year);

            await indicator.getDividends();

            await indicator.createTotalRevenue();

            await indicator.createAssetTurnover();
            await indicator.createBvps();
            await indicator.createCurrentRatio();
            await indicator.createEbit();
            await indicator.createEbitda();
            await indicator.createEbitdaMargin();
            await indicator.createEpsDilutedCurrent();
            await indicator.createEpsLastDate();

            await indicator.createGrossMargin();
            await indicator.createNetDebt();
            await indicator.createNetDebtEbit();
            await indicator.createNetDebtEbitda();
            await indicator.createNetDebtShareholdersEquity();
            await indicator.createNetMargin();
            await indicator.createOperationMargin();
            await indicator.createPayoutRatio();

            await indicator.createRoeRoaRoic();
            // await indicator.createRoe()
            // await indicator.createRoa()
            // await indicator.createRoic()
            await indicator.createShareholderEquityRatio();
            await indicator.createTotalDebtToTotalAssetsRatio();
            // await indicator.createDividendYield()
            await indicator.createMarketCapitalization();
            await indicator.createEbitRatio();
            await indicator.createPe();
            await indicator.createPegRatio();

            await indicator.createPriceWorkingCapitalShare();
            await indicator.createPriceToBook();
            await indicator.createPriceSalesRatio();
            await indicator.createEV();
            await indicator.createEvEbit();

            if (!_.isEqual(oldTicker, indicator.ticker)) {
                const editTicker = await StatisticsOfTicker.findOne({
                    where: {ticker_internal_id: oldTicker.ticker_internal_id},
                });
                if (editTicker) {
                    editTicker?.setAttributes({...indicator.ticker});
                    await editTicker.save();
                    console.log("Update");
                }
            }
        } catch (err: any) {
            console.log("error when try to fill statistic ", err.message);
        }
    }
}
