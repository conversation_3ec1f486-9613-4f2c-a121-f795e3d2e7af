import {Op} from "sequelize";
import {ListOfTickers} from "../../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../../entities/consolidated_data/StatisticsOfTickers";
import {iStatisticsOfTicker} from "../../entities/consolidated_data/iStatisticsOfTicker";
import {ApiEOD} from "../../providers/implements/ApiEOD";
import {iPriceTickerRepository} from "../iPriceTickerRepository";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {RealTimePriceEODHD} from "./RealTimePriceDAO";

export class PriceTickerRepository implements iPriceTickerRepository {
    array_statistics: iStatisticsOfTicker[];
    tickers: ListOfTickers[];
    statisticsToCreate: iStatisticsOfTicker[];
    api: ApiEOD;
    current_valid_prices: RealTimePriceEODHD[];
    current_invalid_prices: RealTimePriceEODHD[];

    constructor() {
        this.api = new ApiEOD();
        this.statisticsToCreate = [];
    }

    async getPrice(updatedTickers: ListOfTickers[]): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        this.tickers = updatedTickers;
        const valid_prices = [];
        const invalid_prices = [];

        for (let i = 0; i < this.tickers.length; i++) {
            const ticker: ListOfTickers = this.tickers[i];
            console.log(`[${i + 1}/${this.tickers.length}] Processing ticker: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            try {
                const response = await this.api.findPrice(ticker.primary_ticker_eodhd);

                if (Array.isArray(response)) {
                    for (let i = 0; i < response?.length; i++) {
                        if (response[i].timestamp !== "NA") {
                            valid_prices.push({...response[i], ticker_internal_id: ticker.id});
                        } else {
                            invalid_prices.push({...response[i], ticker_internal_id: ticker.id});
                        }
                    }
                } else {
                    if (response.timestamp !== "NA") {
                        valid_prices.push({...response, ticker_internal_id: ticker.id});
                    } else {
                        invalid_prices.push({...response, ticker_internal_id: ticker.id});
                    }
                }
            } catch (err: any) {
                console.log("Error price ", ticker.primary_ticker_eodhd, err.message);
                addLogJobExecution(LogLevel.ERROR, "getPrice", "Error when try to get price", {error: err instanceof Error ? err.message : String(err)}, ticker.id);
                continue;
            }
        }
        this.current_valid_prices = valid_prices;
        this.current_invalid_prices = invalid_prices;
        return [valid_prices, invalid_prices];
    }

    async setPrice(tickerPrice: RealTimePriceEODHD): Promise<void> {
        const {ticker_internal_id, code, close, previousClose} = tickerPrice;
        try {
            let price = 0;
            if (close !== "NA" && typeof close === "number") {
                price = close;
            }
            if (close === "NA" && previousClose !== "NA" && typeof previousClose === "number") {
                price = previousClose;
            }
            const statistic = await StatisticsOfTicker.findOne({
                where: {ticker_internal_id: ticker_internal_id || 0},
            });
            if (statistic) {
                statistic.price = price;
                await statistic.save();
            } else {
                const list_of_ticker = await ListOfTickers.findOne({
                    where: {id: ticker_internal_id || 0},
                });

                if (list_of_ticker) {
                    await StatisticsOfTicker.create({
                        price,
                        symbol_code: code,
                        ticker_internal_id: list_of_ticker.id || 0,
                    });
                }
            }
        } catch (err: any) {
            addLogJobExecution(LogLevel.ERROR, "setPrice", "Error when try to set price", {error: err instanceof Error ? err.message : String(err)}, ticker_internal_id || 0);
            console.log("Error when try to set Price ", err.message);
        }
    }

    async getTickers(listOfTickersId?: number[]): Promise<void> {
        const params: any = {};

        const date = new Date();
        const minutes = date.getMinutes();

        const tickers_count = await ListOfTickers.count({
            where: {
                is_enable: 1,
            },
        });

        const mod = tickers_count % 6 || 0;
        const limit = Math.trunc(tickers_count / 6) + mod;

        const page = minutes < 10 ? 0 : Math.floor(minutes / 10);

        if (listOfTickersId && listOfTickersId.length > 0) {
            params.where = {
                id: {
                    [Op.in]: listOfTickersId,
                },
                is_enable: 1,
            };
        } else {
            params.where = {
                is_enable: 1,
            };
            params.order = [["id", "ASC"]];
            params.attributes = ["primary_ticker_eodhd", "id", "symbol_code"];
            params.offset = page * limit;
            params.limit = limit;
        }

        const tickers = await ListOfTickers.findAll(params);

        this.tickers = tickers;
    }
}
